import React from 'react';
import { Helmet } from 'react-helmet';
import HeroSection from './HeroSection';
import WhyWorkWithUs from './WhyWorkWithUs';
import JobOpenings from './JobOpenings';
import ApplicationProcess from './ApplicationProcess';

const CareerPage = () => {
  const jobOpenings = [
    { title: 'Frontend Developer', location: 'Remote or On-site (Lucknow, India)', type: 'Full-time', description: 'We are looking for a skilled Frontend Developer experienced with React and Tailwind CSS to join our dynamic team.' },
    { title: 'Backend Developer', location: 'Remote', type: 'Full-time', description: 'Join our backend team to build efficient, scalable solutions using Node.js, Express, and MongoDB.' },
    { title: 'UI/UX Designer', location: 'On-site (Lucknow, India)', type: 'Contract', description: 'Design beautiful, user-friendly interfaces and work closely with our development team to bring projects to life.' },
    { title: 'Product Manager', location: 'Remote or On-site (Lucknow, India)', type: 'Full-time', description: 'Lead the product development process from ideation to launch, working closely with cross-functional teams.' },
    { title: 'Marketing Specialist', location: 'Remote or On-site (Lucknow, India)', type: 'Part-time', description: 'Help us grow our brand and reach new audiences through strategic marketing campaigns and initiatives.' },
    { title: 'Customer Support Specialist', location: 'Remote or On-site (Lucknow, India)', type: 'Contract', description: 'Provide exceptional customer service and technical support to our users across various platforms.' },
      
  ];

  return (
    <div className="career-page bg-[#fff] py-16 px-6 sm:px-12 lg:px-24">
      <Helmet>
        <title>Careers - Sipher Web</title>
      </Helmet>
      <HeroSection />
      <WhyWorkWithUs />
      <JobOpenings jobs={jobOpenings} />
      <ApplicationProcess />
    </div>
  );
};

export default CareerPage;
