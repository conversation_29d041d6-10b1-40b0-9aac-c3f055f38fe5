const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const nodemailer = require('nodemailer');
require('dotenv').config();

const app = express();

// Middleware
app.use(bodyParser.json());
app.use(
  cors({
    origin: process.env.CORS_ORIGIN || '*',
  })
);

// Simulated database (in-memory storage for demo purposes)
let leads = [];

// Configure nodemailer using environment variables
const transporter = nodemailer.createTransport({
  service: process.env.EMAIL_SERVICE || 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Route to handle form submissions
app.post('/api/leads', async (req, res) => {
  const { name, email, phone, message } = req.body;

  // Basic server-side validation
  if (!name || !email || !phone || !message) {
    return res.status(400).json({ error: 'All fields are required.' });
  }

  // Simulate storing the lead in the database
  const newLead = {
    id: leads.length + 1,
    name,
    email,
    phone,
    message,
    submittedAt: new Date(),
  };

  leads.push(newLead);

  // Prepare the email content
  const mailOptions = {
    from: `"Website Contact Form" <${process.env.EMAIL_USER}>`,
    to: process.env.LEAD_NOTIFY_TO || process.env.EMAIL_USER,
    subject: 'New Lead Submission',
    html: `
      <h1>Contact Form leads</h1>
      <p><strong>Name:</strong> ${name}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Phone:</strong> ${phone}</p>
      <p><strong>Message:</strong> ${message}</p>
      <p><strong>Submitted At:</strong> ${new Date().toLocaleString()}</p>
    `,
  };

  try {
    // Send the email
    await transporter.sendMail(mailOptions);
    console.log('Email sent successfully');

    res.status(200).json({
      message: 'Lead submitted successfully and email sent!',
      lead: newLead,
    });
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({ error: 'Failed to send email' });
  }
});



// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
