/* Importing Tailwind CSS */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Animation for the slide show heading */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 1s ease-in-out forwards;
}

/* Custom animations for landing page */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Map marker pulse animation */
@keyframes ping {
  75%, 100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Page transition animation */
.page-loaded {
  animation: fadeIn 0.6s ease-in-out;
}

/* Optimize images */
img {
  max-width: 100%;
  height: auto;
}

/* Improve image loading */
img[loading="lazy"] {
  transition: opacity 0.3s;
}

img[loading="lazy"]:not([src]) {
  opacity: 0;
}

/* Enhance accessibility */
:focus-visible {
  outline: 2px solid #100562;
  outline-offset: 2px;
}




