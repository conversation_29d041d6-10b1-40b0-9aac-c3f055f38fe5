"use client";

import React, { useState } from "react";
import {
  FaPhone,
  FaWhatsapp,
  FaLinkedinIn,
  FaYoutube,
  FaFacebookF,
  FaInstagram,
} from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";

const socialLinks = [
  { name: "Call", icon: FaPhone, color: "bg-blue-500 hover:bg-blue-600", href: "tel:9125525608",  },
  {
    name: "WhatsApp",
    icon: FaWhatsapp,
    color: "bg-green-500 hover:bg-green-600",
    href: "https://wa.me/919125545607",
    // label: "Enquiry",
  },
  {
    name: "LinkedIn",
    icon: FaLinkedinIn,
    color: "bg-blue-700 hover:bg-blue-800",
    href: "https://www.linkedin.com/company/sipher-web-private-limited/posts/?feedView=all",
    // label: "Follow",
  },
  {
    name: "YouTube",
    icon: FaYoutube,
    color: "bg-red-600 hover:bg-red-700",
    href: "https://youtube.com/@sipherweb?si=15KPTd0gLgI1xJ0r",
    // label: "Subscribe",
  },
  {
    name: "Facebook",
    icon: FaFacebookF,
    color: "bg-blue-600 hover:bg-blue-700",
    href: "https://m.facebook.com/61557833703797/",
    // label: "Follow",
  },
  {
    name: "Instagram",
    icon: FaInstagram,
    color: "bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 hover:from-purple-600 hover:via-pink-600 hover:to-orange-600",
    href: "https://instagram.com/sipherweb/",
    // label: "Follow",
  },

  
];

const StickyIcons = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(null);

  return (
    <motion.div
      initial={false}
      animate={isOpen ? "open" : "closed"}
      className="fixed bottom-8 right-8 z-50 flex flex-col items-end"
    >
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            className="mb-4 bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl p-4 dark:bg-gray-800/90"
          >
            <ul className="flex flex-col gap-3">
              {socialLinks.map((link, index) => (
                <motion.li
                  key={link.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.06 }}
                  onHoverStart={() => setHoveredIndex(index)}
                  onHoverEnd={() => setHoveredIndex(null)}
                >
                  <a
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={link.label}
                    className="flex items-center gap-3 group relative"
                  >
                    <motion.div
                      className={`${link.color} w-10 h-10 rounded-full flex items-center justify-center text-white shadow-md transition-transform duration-200 ${hoveredIndex === index ? 'scale-110' : ''}`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <link.icon className="w-5 h-5" aria-hidden="true" />
                    </motion.div>
                    <motion.span 
                      className="font-medium text-sm text-gray-800 dark:text-gray-200"
                      initial={{ width: 0, opacity: 0 }}
                      animate={hoveredIndex === index ? 
                        { width: 'auto', opacity: 1 } : 
                        { width: 0, opacity: 0 }
                      }
                      transition={{ duration: 0.2 }}
                    >
                      {link.label}
                    </motion.span>
                  </a>
                </motion.li>
              ))}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
      
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsOpen(!isOpen)}
        className={`${
          isOpen ? "bg-gray-700" : "bg-gradient-to-r from-blue-500 to-blue-700"
        } text-white p-4 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 transition-all duration-300 ease-in-out`}
        aria-label={isOpen ? "Close social links" : "Open social links"}
      >
        <motion.svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          aria-hidden="true"
        >
          {isOpen ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          )}
        </motion.svg>
      </motion.button>
    </motion.div>
  );
};

export default StickyIcons;
