import React from "react";
import logo from "../assets/SipherWebLogo.png";

const Loader = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white z-50 overflow-hidden">
      <div className="relative w-full max-w-sm">
        {/* Main Loader Container */}
        <div className="relative w-48 h-48 mx-auto">
          {/* Spinning Rings */}
          <div className="absolute inset-0 rounded-full border-4 border-t-[#100562] border-r-[#100562] border-b-transparent border-l-transparent animate-spin"></div>
          <div className="absolute inset-2 rounded-full border-4 border-t-[#FFE300] border-r-[#FFE300] border-b-transparent border-l-transparent animate-spin-reverse"></div>
          <div className="absolute inset-4 rounded-full border-4 border-t-[#100562] border-r-[#100562] border-b-transparent border-l-transparent animate-spin-slow"></div>

          {/* Pulsing Circle */}
          <div className="absolute inset-8 rounded-full bg-white shadow-lg flex items-center justify-center animate-pulse">
            {/* Company Logo */}
            <img
              src={logo || "/placeholder.svg"}
              alt="Sipher Web Logo"
              className="w-24 h-24 animate-fade-in"
            />
          </div>
        </div>

        {/* Loading Text */}
        <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 w-full text-center">
          <div className="relative inline-block">
            <span className="text-[#100562] font-medium tracking-wider animate-pulse">
              Loading
            </span>
            <span className="inline-flex ml-1">
              <span className="animate-bounce delay-0">.</span>
              <span className="animate-bounce delay-100">.</span>
              <span className="animate-bounce delay-200">.</span>
            </span>
          </div>
        </div>

        {/* Floating Particles */}
        <div className="absolute -inset-24 pointer-events-none">
          {[...Array(window.innerWidth < 640 ? 5 : 10)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 md:w-3 md:h-3 rounded-full animate-float"
              style={{
                background: i % 2 ? "#100562" : "#FFE300",
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animationDelay: `${i * 0.3}s`,
                opacity: 0.3,
              }}
            ></div>
          ))}
        </div>
      </div>

      {/* Custom Animations */}
      <style jsx>{`
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        @keyframes spin-reverse {
          from {
            transform: rotate(360deg);
          }
          to {
            transform: rotate(0deg);
          }
        }

        @keyframes float {
          0%,
          100% {
            transform: translateY(0) scale(1);
          }
          50% {
            transform: translateY(-10px) scale(1.05);
          }
        }

        @keyframes fade-in {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }

        .animate-spin {
          animation: spin 2s linear infinite;
        }

        .animate-spin-reverse {
          animation: spin-reverse 1.5s linear infinite;
        }

        .animate-spin-slow {
          animation: spin 3s linear infinite;
        }

        .animate-float {
          animation: float 2s ease-in-out infinite;
        }

        .animate-fade-in {
          animation: fade-in 0.8s ease-out forwards;
        }

        .delay-0 {
          animation-delay: 0s;
        }
        .delay-100 {
          animation-delay: 0.1s;
        }
        .delay-200 {
          animation-delay: 0.2s;
        }

        @media (max-width: 640px) {
          .animate-float {
            animation-duration: 1.5s;
          }
        }
      `}</style>
    </div>
  );
};

export default Loader;
