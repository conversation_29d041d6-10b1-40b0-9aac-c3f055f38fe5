import React, { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Header from "./layout/Header";
import Footer from "./layout/Footer";
import Home from "./pages/Home";
import ServicePage from "./pages/Services";
import PackagesPage from "./pages/Packages";
import ContactPage from "./pages/Contact";
import CareerPage from "./pages/Career";
import AboutUsPage from "./pages/About";
import LiveChat from "./layout/LiveChat";
import AcademyButton from "./layout/AcademyButton";
import Portfolio from "./pages/Portfolio";
import Loader from "./components/Loader";
import CommunityPage from "./pages/Community";
import ProductPage from "./pages/Products";
import EnhancedCursor from "./layout/MouseCursorEffect";

function App() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => setLoading(false), 1000); // Adjust duration as needed
    return () => clearTimeout(timer); // Clean up the timer
  }, []);

  return (
    <Router>
      <div className="App relative overflow-hidden">
        <EnhancedCursor/>
        {loading && <Loader />} {/* Show loader while loading */}
        {!loading && (
          <>
            <Routes>
              <Route path="/" element={
                <>
                  <Header />
                  <main className="p-0 m-0">
                    <Home />
                  </main>
                  <Footer />
                  <LiveChat />
                  <AcademyButton />
                </>
              } />
              <Route path="*" element={
                <>
                  <Header />
                  <main className="p-0 m-0">
                    <Routes>
                      <Route path="/about" element={<AboutUsPage />} />
                      <Route path="/services" element={<ServicePage />} />
                      <Route path="/packages" element={<PackagesPage />} />
                      <Route path="/contact" element={<ContactPage />} />
                      <Route path="/career" element={<CareerPage />} />
                      <Route path="/portfolio" element={<Portfolio />} />
                      <Route path="/community" element={<CommunityPage />} />
                      <Route path="/products" element={<ProductPage />} />
                    </Routes>
                  </main>
                  <Footer />
                  <LiveChat />
                  <AcademyButton />
                </>
              } />
            </Routes>
          </>
        )}
      </div>
    </Router>
  );
}

export default App;
