import React from 'react';
import { Helmet } from 'react-helmet';
import { FaCheckCircle } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const services = [
  {
    id: 1,
    title: "Mobile App Development",
    description: "Build native and cross-platform mobile applications with custom features to meet your business needs.",
    features: [
      "Native and Cross-Platform Development",
      "Seamless Performance",
      "Intuitive and User-Friendly Designs",
      "Custom Features and Integrations",
      "Regular Updates and Maintenance",
      "App Store and Play Store Submission",
      "Post-launch Support"
    ],
    price: "₹19,999/- (Basic) | ₹29,999/- (Advanced)",
    color: "bg-red-600",
  },
  {
    id: 2,
    title: "Web Development",
    description: "Create responsive, dynamic, and scalable websites and web applications tailored to your business goals.",
    features: [
      "Responsive Design",
      "Dynamic Web Applications",
      "Scalable Solutions for Growth",
      "SEO Optimization",
      "E-commerce Integration",
      "Content Management System (CMS) Integration",
      "Security Enhancements"
    ],
    price: "₹16,999/- (Basic) | ₹24,999/- (Advanced)",
    color: "bg-green-600",
  },
  {
    id: 3,
    title: "Custom Software Development",
    description: "Develop bespoke software solutions that fit your exact business needs and processes, enhancing your operational efficiency.",
    features: [
      "End-to-End Solutions from Concept to Launch",
      "Scalable and Secure",
      "High-Performance Architecture",
      "Custom Integrations with Existing Systems",
      "Ongoing Support and Maintenance",
      "User-Centric Design and Testing"
    ],
    price: "₹27,999/- (Basic) | ₹39,999/- (Enterprise)",
    color: "bg-yellow-500",
  },
  {
    id: 4,
    title: "Software Product Development",
    description: "Create innovative software products that meet market demands and provide an outstanding user experience.",
    features: [
      "Market-Aligned Solutions",
      "Innovative and Scalable",
      "User Satisfaction Focus",
      "Agile Development Methodology",
      "Product Lifecycle Management",
      "Quality Assurance Testing"
    ],
    price: "₹39,999/- (Basic) | ₹59,999/- (Premium)",
    color: "bg-teal-600",
  },
  {
    id: 5,
    title: "ERP Software Development",
    description: "Streamline your business operations with a custom ERP solution tailored to your organizational needs and objectives.",
    features: [
      "Streamline Operations and Processes",
      "Improve Productivity and Efficiency",
      "Better Resource Planning and Management",
      "Custom Modules for Different Departments",
      "Integration with Existing Systems",
      "Ongoing Support and Updates"
    ],
    price: "₹49,999/- (Basic) | ₹69,999/- (Advanced)",
    color: "bg-indigo-600",
  },
  {
    id: 6,
    title: "UI/UX Design",
    description: "Design intuitive and user-centric interfaces that provide a seamless experience across all platforms.",
    features: [
      "User-Centered Design Approach",
      "Research and Prototyping",
      "Engaging and Aesthetic Visual Designs",
      "Responsive and Adaptive Layouts",
      "Usability Testing for User Feedback",
      "Brand Consistency and Identity"
    ],
    price: "₹14,999/- (Basic) | ₹24,999/- (Advanced)",
    color: "bg-purple-600",
  },
  {
    id: 7,
    title: "Software Maintenance",
    description: "Ensure your software and applications perform optimally with our ongoing maintenance and support services.",
    features: [
      "Bug Fixes and Performance Optimization",
      "Regular Software Updates and Patches",
      "Security Enhancements and Monitoring",
      "24/7 Customer Support",
      "Backup and Recovery Solutions"
    ],
    price: "₹11,999/- (Basic) | ₹18,999/- (Comprehensive)",
    color: "bg-pink-600",
  },
  {
    id: 8,
    title: "Cybersecurity Solutions",
    description: "Protect your business with advanced cybersecurity measures, safeguarding your data and systems from evolving threats.",
    features: [
      "Vulnerability Assessments and Threat Detection",
      "Advanced Threat Management",
      "Secure Software Development Practices",
      "Incident Response and Recovery",
      "Compliance Audits and Certifications"
    ],
    price: "₹22,999/- (Basic) | ₹34,999/- (Enterprise)",
    color: "bg-orange-600",
  },
  {
    id: 9,
    title: "Cloud Solutions",
    description: "Migrate, manage, and optimize your infrastructure with secure and cost-effective cloud solutions.",
    features: [
      "Secure Cloud Migration and Management",
      "Optimized Cost-Effective Cloud Solutions",
      "Cloud Integration with AWS, Azure, Google Cloud",
      "Disaster Recovery and Business Continuity",
      "Scalable Cloud Infrastructure"
    ],
    price: "₹24,999/- (Basic) | ₹39,999/- (Advanced)",
    color: "bg-blue-600",
  },
  {
    id: 10,
    title: "Digital Marketing",
    description: "Increase your online presence with our comprehensive digital marketing strategies, driving traffic and engagement.",
    features: [
      "SEO and PPC Advertising Campaigns",
      "Content Marketing and Blogging",
      "Email Marketing Campaigns",
      "Social Media Marketing and Influencer Partnerships",
      "Analytics and Conversion Optimization"
    ],
    price: "₹17,999/- (Basic) | ₹29,999/- (Advanced)",
    color: "bg-red-600",
  },
  {
    id: 11,
    title: "Social Media Marketing",
    description: "Maximize your brand's visibility and engagement across multiple social media platforms with expert strategies.",
    features: [
      "Targeted Content Creation and Distribution",
      "Paid Ad Campaigns for Social Platforms",
      "Social Media Analytics and Monitoring",
      "Influencer Marketing and Brand Collaboration",
      "Community Management and Engagement"
    ],
    price: "₹15,999/- (Basic) | ₹22,999/- (Advanced)",
    color: "bg-green-600",
  },
  {
    id: 12,
    title: "Social Media Management",
    description: "Manage and maintain a consistent and engaging social media presence with expert content strategies and scheduling.",
    features: [
      "Content Creation and Scheduling",
      "Audience Engagement and Interaction",
      "Performance Monitoring and Insights",
      "Brand Consistency Across Platforms",
      "Social Media Growth Strategy"
    ],
    price: "₹13,999/- (Basic) | ₹21,999/- (Pro)",
    color: "bg-yellow-500",
  },
  {
    id: 13,
    title: "SEO Optimization",
    description: "Increase organic traffic and improve your search engine rankings with a full range of SEO services.",
    features: [
      "Keyword Research and Strategy",
      "On-Page and Off-Page SEO Optimization",
      "Technical SEO Audits and Fixes",
      "Content Creation and Link Building",
      "Monthly Reporting and Analysis"
    ],
    price: "₹16,999/- (Basic) | ₹26,999/- (Premium)",
    color: "bg-teal-600",
  },
  {
    id: 14,
    title: "Data Analytics",
    description: "Transform your data into actionable insights, helping you make data-driven decisions and strategies.",
    features: [
      "Data Collection and Storage",
      "Data Analysis and Visualization",
      "Actionable Insights for Decision-Making",
      "Predictive Analytics and Forecasting",
      "Custom Dashboards for Key Metrics"
    ],
    price: "₹18,999/- (Basic) | ₹29,999/- (Advanced)",
    color: "bg-indigo-600",
  },
  {
    id: 15,
    title: "E-Commerce Solutions",
    description: "Develop and optimize your e-commerce platform to enhance user experience and increase online sales.",
    features: [
      "Custom Shopping Cart Solutions",
      "Integrated Payment Gateways",
      "Mobile-Friendly and Responsive Designs",
      "Inventory Management and Analytics",
      "Customer Relationship Management (CRM) Integration"
    ],
    price: "₹22,999/- (Basic) | ₹39,999/- (Enterprise)",
    color: "bg-purple-600",
  },
  {
    id: 16,
    title: "IT Consulting",
    description: "Transform your business with expert IT strategy, enhancing your technology infrastructure and processes.",
    features: [
      "Identify IT Challenges and Opportunities",
      "Propose Tailored IT Solutions",
      "Optimize IT Infrastructure for Efficiency",
      "Technology Roadmap and Future Planning",
      "Implementation and Ongoing Support"
    ],
    price: "₹19,999/- (Basic) | ₹29,999/- (Advanced)",
    color: "bg-pink-600",
  },
  {
    id: 17,
    title: "Digital Transformation",
    description: "Revamp your business operations with cutting-edge digital transformation strategies and solutions.",
    features: [
      "Cloud Migration and Integration",
      "Automation of Business Processes",
      "Data Analytics for Growth",
      "Change Management and Employee Training",
      "Process Optimization and Improvement"
    ],
    price: "₹25,999/- (Basic) | ₹39,999/- (Enterprise)",
    color: "bg-orange-600",
  },
  {
    id: 18,
    title: "Mobile App Marketing",
    description: "Promote your mobile app and expand your user base with targeted mobile marketing campaigns.",
    features: [
      "App Store Optimization (ASO)",
      "Social Media Campaigns and Influencers",
      "User Acquisition Strategies",
      "Retention Marketing and Loyalty Programs",
      "In-depth Analytics and Campaign Tracking"
    ],
    price: "₹19,999/- (Basic) | ₹27,999/- (Premium)",
    color: "bg-blue-600",
  }
];



const PackagesPage = () => {
  const navigate = useNavigate();

  return (
    <div className="packages-page py-12 bg-gradient-to-b from-[#fff] to-gray-400">
      <Helmet>
        <title>Package - Sipher Web</title>
      </Helmet>
      <div className="text-center mb-12 mt-6">
        <h2 className="text-3xl sm:text-4xl font-extrabold text-[#100562] mb-4">
          Our Packages
        </h2>
        <p className="text-md sm:text-lg text-[#100562] max-w-3xl mx-auto">
          <strong>Choose the perfect package</strong> for your business, tailored to meet your needs.
        </p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 px-4 sm:px-8 lg:px-16">
        {services.map((service, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ scale: 1.05, rotate: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`package-card p-4 sm:p-6 ${service.color} text-white rounded-xl shadow-lg hover:shadow-2xl transition-transform transform hover:scale-105`}
          >
            <h3 className="text-xl font-bold mb-2">{service.title}</h3>
            <p className="text-lg font-bold mb-3">{service.price}</p>
            <p className="text-sm mb-3">{service.description}</p>
            <ul className="mb-4 space-y-1">
              {service.features.map((feature, idx) => (
                <li key={idx} className="flex items-center">
                  <FaCheckCircle className="text-white mr-2" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
            <a
              href="/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-[#100562] px-3 py-1 rounded-full shadow-md hover:bg-opacity-90 transition-all transform hover:scale-105 text-sm"
            >
              Get Started
            </a>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default PackagesPage;
