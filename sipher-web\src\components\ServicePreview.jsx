import React from 'react';
import { 
  Code2, Globe2, Smartphone, Cloud, Database, Shield,
  Zap, BarChart, Users2, Rocket, ArrowRight
} from 'lucide-react';

const ServicesSection = () => {
  const services = [
    {
      icon: <Code2 className="w-8 h-8" />,
      title: "Web Development",
      description: "Custom web applications built with cutting-edge technologies",
      features: ["Responsive Design", "SEO Optimization", "Performance Tuning"],
      color: "blue"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Mobile Development",
      description: "Native and cross-platform mobile applications",
      features: ["iOS & Android", "Real-time Updates", "Offline Support"],
      color: "green"
    },
    {
      icon: <Cloud className="w-8 h-8" />,
      title: "Cloud Solutions",
      description: "Scalable cloud infrastructure and deployment",
      features: ["Auto-scaling", "Load Balancing", "High Availability"],
      color: "purple"
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Database Design",
      description: "Optimized database architecture and management",
      features: ["Data Modeling", "Performance", "Security"],
      color: "orange"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Cybersecurity",
      description: "Enterprise-grade security solutions",
      features: ["Threat Detection", "Data Protection", "Compliance"],
      color: "red"
    },
    {
      icon: <BarChart className="w-8 h-8" />,
      title: "Analytics",
      description: "Data-driven insights and reporting",
      features: ["Real-time Analytics", "Custom Reports", "Visualizations"],
      color: "indigo"
    }
  ];

  const colorVariants = {
    blue: "from-blue-500 to-blue-600",
    green: "from-green-500 to-green-600",
    purple: "from-purple-500 to-purple-600",
    orange: "from-orange-500 to-orange-600",
    red: "from-red-500 to-red-600",
    indigo: "from-indigo-500 to-indigo-600"
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
            <Rocket className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-semibold text-blue-600">Our Services</span>
          </div>
          
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Comprehensive Digital
            <span className="relative ml-2">
              Solutions
              <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FFE300] transform -skew-x-12 opacity-80"></div>
            </span>
          </h2>
          
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            From web development to cloud solutions, we offer a full range of digital services
            to help your business thrive in the digital age.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {/* Service Icon */}
              <div className={`p-6 bg-gradient-to-r ${colorVariants[service.color]} text-white`}>
                <div className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  {service.icon}
                </div>
              </div>

              {/* Service Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>

                {/* Features List */}
                <ul className="space-y-3 mb-6">
                  {service.features.map((feature, i) => (
                    <li key={i} className="flex items-center gap-2 text-gray-600">
                      <Zap className="w-4 h-4 text-blue-600" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Learn More Link */}
                <a
                  href={`/services#${service.title.toLowerCase().replace(' ', '-')}`}
                  className="inline-flex items-center gap-2 text-blue-600 font-medium hover:gap-3 transition-all duration-300"
                >
                  Learn More
                  <ArrowRight className="w-5 h-5" />
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <a
            href="/contact"
             className="inline-flex items-center gap-2 px-8 py-4 bg-[#100562] text-white rounded-xl hover:bg-blue-700 transition-all duration-300"
          >
            <span className="flex items-center gap-2">
              Get Started
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </span>
          </a>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;