import React, { useState } from "react";

const ContactForm = () => {
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        phone: "",
        message: "",
    });
    const [errors, setErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [submitError, setSubmitError] = useState("");

    const handleChange = (e) => {
        const { id, value } = e.target;
        setFormData({ ...formData, [id]: value });
        if (isSubmitted) setIsSubmitted(false); // Reset success message on change
    };

    const validateForm = () => {
        const newErrors = {};
        if (!formData.name.trim()) newErrors.name = "Name is required.";
        if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email))
            newErrors.email = "A valid email is required.";
        if (!formData.phone.trim() || !/^\d{10}$/.test(formData.phone))
            newErrors.phone = "Phone number must be 10 digits.";
        if (!formData.message.trim())
            newErrors.message = "Message cannot be empty.";
        return newErrors;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        setErrors({});
        setIsSubmitting(true);
        setSubmitError("");

        try {
            const apiBase = import.meta.env.VITE_API_BASE || "http://localhost:5000";
            const response = await fetch(`${apiBase}/api/leads`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                throw new Error("Failed to submit form");
            }

            const result = await response.json();
            setIsSubmitted(true);
            setFormData({ name: "", email: "", phone: "", message: "" });
            console.log(result.message);
        } catch (error) {
            console.error("Error:", error);
            setSubmitError("Failed to send the message. Please try again.");
        } finally {
            setIsSubmitting(false);
            setTimeout(() => setIsSubmitted(false), 3000); // Clear success message after 3 seconds
        }
    };

    return (
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-md max-w-md mx-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Send Us a Message</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                        <input
                            type="text"
                            id="name"
                            value={formData.name}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-200'} focus:ring-2 focus:ring-blue-100 focus:border-[#100562] transition-all duration-300`}
                            placeholder="John Doe"
                        />
                        {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                    </div>
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input
                            type="email"
                            id="email"
                            value={formData.email}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-200'} focus:ring-2 focus:ring-blue-100 focus:border-[#100562] transition-all duration-300`}
                            placeholder="<EMAIL>"
                        />
                        {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                    </div>
                </div>

                <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                    <input
                        type="tel"
                        id="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className={`w-full px-3 py-2 rounded-lg border ${errors.phone ? 'border-red-500' : 'border-gray-200'} focus:ring-2 focus:ring-blue-100 focus:border-[#100562] transition-all duration-300`}
                        placeholder="e.g., 9125545607"
                    />
                    {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                </div>

                <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                    <textarea
                        id="message"
                        rows="3"
                        value={formData.message}
                        onChange={handleChange}
                        className={`w-full px-3 py-2 rounded-lg border ${errors.message ? 'border-red-500' : 'border-gray-200'} focus:ring-2 focus:ring-blue-100 focus:border-[#100562] transition-all duration-300`}
                        placeholder="Tell us about your project..."
                    ></textarea>
                    {errors.message && <p className="text-red-500 text-xs mt-1">{errors.message}</p>}
                </div>

                <button
                    type="submit"
                    className={`w-full bg-[#100562] text-white py-3 rounded-lg hover:bg-blue-700 transition-all duration-300 flex items-center justify-center gap-2 group ${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}
                >
                    {isSubmitting ? "Sending..." : "Send Message"}
                </button>
                {isSubmitted && <p className="text-green-500 text-xs mt-3">Message sent successfully!</p>}
                {submitError && <p className="text-red-500 text-xs mt-3">{submitError}</p>}
            </form>
        </div>
    );
};

export default ContactForm;
