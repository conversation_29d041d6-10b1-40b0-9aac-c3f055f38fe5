# Sipher Web Tech

Monorepo with a React (Vite + Tailwind) frontend and a Node.js/Express backend for handling contact form leads.

## Folders
- `sipher-web/` — Frontend app
- `sipher-backend/` — Backend API

## Getting started
1. In each folder, copy `.env.example` to `.env` and fill values.
2. Install dependencies in each folder.
3. Run backend first, then frontend.

## Environment
- Frontend: `VITE_API_BASE` (default `http://localhost:5000`)
- Backend: `PORT`, `CORS_ORIGIN`, `EMAIL_SERVICE`, `EMAIL_USER`, `EMAIL_PASS`, `LEAD_NOTIFY_TO`

Keep secrets out of version control. Use the `.env.example` templates.
