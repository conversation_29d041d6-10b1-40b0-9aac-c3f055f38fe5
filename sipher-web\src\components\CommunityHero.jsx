import React from 'react';
import { Users2, <PERSON>rk<PERSON>, <PERSON>2, <PERSON><PERSON><PERSON> } from 'lucide-react';

const CommunityHero = () => {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50 via-white to-transparent"></div>
      <div className="absolute inset-0" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
        backgroundSize: '48px 48px',
        opacity: '0.05'
      }}></div>

      <div className="container mx-auto px-4 relative">
        <div className="max-w-5xl mx-auto text-center">
          {/* Community Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-8">
            <Users2 className="w-5 h-5 text-[#100562]" />
            <span className="text-sm font-semibold text-[#100562]">Join Our Global Community</span>
          </div>

          {/* Main Title */}
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Where Innovation Meets
            <span className="relative block mt-2">
              Collaboration
              <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FFE300] transform -skew-x-12 opacity-80"></div>
            </span>
          </h1>

          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
            Connect with developers, designers, and innovators worldwide. Share knowledge,
            collaborate on projects, and grow together in our thriving community.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <a
              href="#join"
              className="group px-8 py-4 bg-[#100562] text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="flex items-center gap-2">
                Join Community
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </span>
            </a>
            <a
              href="#explore"
              className="group px-8 py-4 bg-white text-[#100562] rounded-xl border-2 border-[#100562] hover:bg-blue-50 transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="flex items-center gap-2">
                Explore Resources
                <Sparkles className="w-5 h-5" />
              </span>
            </a>
          </div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16">
            {[
              {
                icon: <Globe2 className="w-6 h-6" />,
                title: "Global Network",
                description: "Connect with members from over 150+ countries"
              },
              {
                icon: <Sparkles className="w-6 h-6" />,
                title: "Expert Insights",
                description: "Access exclusive workshops and mentorship"
              },
              {
                icon: <Users2 className="w-6 h-6" />,
                title: "Active Forums",
                description: "Engage in discussions with industry experts"
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-4 mx-auto text-[#100562]">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export default CommunityHero;