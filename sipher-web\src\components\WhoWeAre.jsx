import React from 'react';
import { <PERSON>R<PERSON>, Target, Rocket, CheckCircle2 } from 'lucide-react';
import missionIMage from '../assets/mission-image.jpg';

const AboutUs = () => {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Decorative Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-white to-purple-50/30" />
      <div className="absolute top-0 left-0 w-full h-full" 
        style={{
          backgroundImage: "radial-gradient(circle at 20px 20px, #f1f5f9 2px, transparent 0)",
          backgroundSize: "40px 40px"
        }}
   />

      <div className="container mx-auto px-4 relative">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-16 relative">
            

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8 animate-slide-up">
              Crafting Digital Excellence
              <span className="block mt-2 text-[#100560]">For Over 15 Years</span>
            </h2>

            <p className="text-lg text-gray-600 leading-relaxed mb-8 animate-fade-in">
              At Sipher Web, we craft digital experiences that transform businesses. Explore our journey of innovation 
              and excellence that makes us a trusted partner worldwide.
            </p>
          </div>

          {/* Vision & Mission Section */}
    <div className="grid md:grid-cols-2 gap-8 mb-20">
    {/* Vision */}
     <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 border border-gray-100 overflow-hidden animate-slide-up">
      <img src="https://img.freepik.com/premium-photo/illuminating-innovation-glowing-concept-creative-ideas_1297526-7601.jpg?w=826" alt="Vision" className="rounded-lg mb-4" />
      <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <Target className="w-6 h-6 text-[#FFE300]" />
      Our Vision
    </h3>
    <p className="text-gray-600 leading-relaxed mb-4">
      To be the catalyst for digital transformation, empowering businesses to thrive in the digital age through 
      innovative solutions and unparalleled expertise.
    </p>
    <p className="text-gray-600 leading-relaxed">
      We envision a world where businesses of all sizes can unlock their full potential by embracing cutting-edge 
      technology, leveraging data-driven insights, and staying ahead in a rapidly evolving digital landscape. 
      Our focus is on fostering sustainable growth and creating impactful solutions that redefine industry standards.
    </p>
  </div>

  {/* Mission */}
  <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 border border-gray-100 overflow-hidden animate-slide-up delay-200">
    <img src={missionIMage}  alt="Mission" className="rounded-lg mb-4" />
    <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <Rocket className="w-6 h-6 text-[#FFE300]" />
      Our Mission
    </h3>
    <p className="text-gray-600 leading-relaxed mb-4">
      To deliver exceptional digital solutions that drive growth, enhance efficiency, and create lasting value 
      for our clients through innovation and technical excellence.
    </p>
    <p className="text-gray-600 leading-relaxed">
      Our mission is to combine creativity and technology to build user-centric solutions that solve real-world 
      problems. By cultivating strong partnerships and prioritizing continuous improvement, we aim to empower 
      businesses with scalable, reliable, and future-ready tools. We are dedicated to building trust, inspiring 
      innovation, and driving measurable results.
    </p>
  </div>
</div>


{/* Statistics Section */}
<div className="relative bg-[#100560] rounded-2xl p-8 mb-16 overflow-hidden">
  <div className="absolute inset-0 bg-pattern opacity-10" />
  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
    {[
      { value: "15+", label: "Years of Excellence" },
      { value: "500+", label: "Projects Delivered" },
      { value: "98%", label: "Client Satisfaction" },
      { value: "24/7", label: "Expert Support" },
    ].map((stat, index) => (
      <div
        key={index}
        className="relative text-center bg-white/10 p-4 rounded-md transition-all duration-300 hover:bg-white/30 hover:backdrop-blur-md hover:scale-105"
        style={{ animationDelay: `${index * 200}ms` }}
      >
        <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
        <div className="text-blue-100 text-sm">{stat.label}</div>
      </div>
    ))}
  </div>
</div>


          {/* Why Choose Us */}
          <div className="bg-white rounded-2xl p-8 md:p-12 shadow-lg border border-gray-100 mb-20">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Why Choose Sipher Web?</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                "Industry-leading expertise",
                "Cutting-edge technologies",
                "Dedicated support team",
                "Scalable solutions",
                "Competitive pricing",
                "Proven track record",
                "Client-centric approach",
                "Innovative strategies",
                "Transparent processes",
                "Agile methodologies",
                "Comprehensive services",
                "Sustainable practices"
              ].map((item, index) => (
                <div key={index} className="flex items-center gap-3 animate-fade-in" 
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <CheckCircle2 className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">{item}</span>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <a
              href="/contact"
              className="group inline-flex items-center gap-2 px-8 py-4 bg-[#100560] text-white rounded-full hover:bg-black transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl animate-fade-in"
            >
              Start Your Journey
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUs;
