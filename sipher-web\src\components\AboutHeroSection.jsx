"use client";

import React, { useEffect, useRef } from "react";
import { motion, useAnimation, useInView } from "framer-motion";
import { ArrowRight, Award, Users, Zap } from "lucide-react";

const AboutUsHero = () => {
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: true });

  useEffect(() => {
    if (inView) {
      controls.start("visible");
    }
  }, [controls, inView]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100, damping: 10 },
    },
  };

  return (
    <section className="relative pt-24 pb-16 overflow-hidden bg-gray-50">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50 via-white to-transparent"></div>
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
          backgroundSize: '36px 36px',
          opacity: '0.05',
        }}
      ></div>

      <div className="container mx-auto px-4 relative">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main Title */}
          <motion.div
            ref={ref}
            initial="hidden"
            animate={controls}
            variants={containerVariants}
            className="space-y-8"
          >
            <motion.h1
              variants={itemVariants}
              className="text-4xl md:text-6xl font-bold text-gray-900 mb-4"
            >
              Empowering Your{" "}
              <span className="relative inline-block mt-2">
                Digital Future
                <div className="absolute left-0 -bottom-1 w-full h-1.5 bg-[#FFE300] transform -skew-x-12 opacity-80"></div>
              </span>
            </motion.h1>
            <motion.p
              variants={itemVariants}
              className="text-base md:text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed"
            >
              We're not just a company, we're your partners in innovation. Discover how we're shaping the digital
              landscape and driving success for businesses worldwide.
            </motion.p>
            <motion.div
              variants={itemVariants}
              className="flex flex-col sm:flex-row items-center justify-center gap-4"
            >
              <a
                href="/contact"
                className="group px-6 py-3 bg-[#100562] text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-blue-300"
              >
                <span className="flex items-center gap-2">
                  Get Started
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
              </a>
              <a
                href="/services"
                className="group px-6 py-3 bg-white text-[#100562] rounded-lg border border-[#100562] hover:bg-blue-50 transition-all duration-300 transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-blue-100"
              >
                <span className="flex items-center gap-2">
                  Our Services
                </span>
              </a>
            </motion.div>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial="hidden"
            animate={controls}
            variants={containerVariants}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8"
          >
            {[
              { icon: Zap, title: "Innovative Solutions", description: "Cutting-edge tech for modern challenges" },
              { icon: Users, title: "Client-Centric", description: "Your success is our top priority" },
              { icon: Award, title: "Industry Leaders", description: "Recognized excellence in digital innovation" },
              { icon: ArrowRight, title: "Forward-Thinking", description: "Shaping the future of technology" },
            ].map((item, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-2"
              >
                <item.icon className="h-8 w-8 text-[#100562] mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </motion.div>
            ))}
          

          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutUsHero;