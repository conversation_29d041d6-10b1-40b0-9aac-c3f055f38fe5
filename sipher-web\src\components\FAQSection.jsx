import React, { useState } from 'react';
import { Plus, Minus, HelpCircle, Search, MessageCircle } from 'lucide-react';
import GetStart from './GetStartForm'

const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const faqs = [
    {
      question: "How do I start a project with you?",
      answer: `Getting started is easy. Just contact us through our website or email with your project idea. We’ll schedule a quick consultation to understand your needs, and from there, we’ll provide a plan with timelines and costs.`,
    },
    {
      question: "Can I get a fixed cost estimate for my project?",
      answer: `Yes, after understanding your project requirements, we provide a detailed quote. If your project scope changes during development, we’ll discuss adjustments and keep you informed.`,
    },
    {
      question: "What if I don’t know exactly what I need for my business?",
      answer: `That’s completely fine. We’re here to guide you. During our consultation, we’ll discuss your goals and challenges to recommend the best solution tailored to your needs.`,
    },
    {
      question: "How long does it take to complete a website or app?",
      answer: `The timeline depends on the complexity of your project. A basic website can take 2–4 weeks, while a custom app or complex platform may take several months. We’ll provide an estimated timeline during our initial discussion.`,
    },
    {
      question: "Will I be involved during the development process?",
      answer: `Absolutely. We keep you updated at every stage of the project with regular meetings, progress reports, and previews. Your feedback is essential to ensure we’re building exactly what you need.`,
    },
    {
      question: "Can you update or fix my existing website or app?",
      answer: `Yes, we can review your existing solution and help with updates, bug fixes, or improvements to meet modern standards and improve performance.`,
    },
    {
      question: "Do you offer support after the project is finished?",
      answer: `Yes, we provide post-launch support, including bug fixes, updates, and even new features if needed. Our goal is to ensure your solution stays up-to-date and effective.`,
    },
    {
      question: "What if I want to add new features after the project is completed?",
      answer: `No problem! We offer ongoing development services, so you can scale your solution as your business grows. Just let us know what you need, and we’ll make it happen.`,
    },
    {
      question: "Do you handle hosting and domain registration?",
      answer: `Yes, we can help you set up reliable hosting and register your domain. We also recommend hosting options based on your needs to ensure fast performance and high uptime.`,
    },
    {
      question: "Are your websites and apps mobile-friendly?",
      answer: `Yes, all our solutions are fully responsive and optimized for all devices, including desktops, tablets, and smartphones.`,
    },
    {
      question: "What payment methods do you accept?",
      answer: `We accept payments via bank transfers, UPI, and other standard methods. Payment terms are discussed upfront and outlined in our agreement.`,
    },
    {
      question: "Do you work with small businesses or startups?",
      answer: `Yes, we work with businesses of all sizes, from small startups to large enterprises. We tailor our solutions to fit your specific goals and budget.`,
    },
  ];

  const filteredFaqs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <section className="relative py-12 bg-gradient-to-b from-gray-50 to-white">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(#100562 0.5px, transparent 0.5px)`,
          backgroundSize: '24px 24px',
          opacity: '0.05'
        }}></div>
      </div>

      <div className="container mx-auto px-2 relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 rounded-lg mb-4">
              <HelpCircle className="w-3 h-3 text-[#100562]" />
              <span className="text-xs font-semibold text-[#100562]">
                Knowledge Base
              </span>
            </div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            
            <p className="text-md text-gray-600 mb-6">
              Find comprehensive answers to common questions about our enterprise solutions
            </p>

            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto mb-6">
              <div className="absolute inset-y-0 left-2 flex items-center pointer-events-none">
                <Search className="w-4 h-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search for answers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-8 pr-2 py-2 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-100 focus:border-[#100562] transition-all duration-300 placeholder-gray-400"
              />
            </div>
          </div>

          {/* FAQ Items */}
          <div className="space-y-2">
            {filteredFaqs.map((faq, index) => (
              <div
                key={index}
                className="group bg-white rounded-xl border border-gray-100 hover:border-[#100562] transition-all duration-300"
              >
                <button
                  onClick={() => setOpenIndex(openIndex === index ? null : index)}
                  className="w-full flex items-center justify-between p-4 text-left"
                >
                  <h3 className="text-md font-semibold text-gray-900 pr-4 group-hover:text-[#100562] transition-colors duration-300">
                    {faq.question}
                  </h3>
                  <div className={`flex-shrink-0 p-1 rounded-full bg-gray-50 group-hover:bg-blue-50 transition-all duration-300`}>
                    {openIndex === index ? (
                      <Minus className="w-4 h-4 text-[#100562]" />
                    ) : (
                      <Plus className="w-4 h-4 text-[#100562]" />
                    )}
                  </div>
                </button>
                
                <div
                  className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    openIndex === index ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                  }`}
                >
                  <div className="p-4 pt-0 border-t border-gray-100">
                    <p className="text-gray-600 leading-relaxed text-sm">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Contact Support */}
          <div className="mt-8 text-center p-4 bg-blue-50 rounded-2xl">
            <div className="flex items-center justify-center mb-2">
              <MessageCircle className="w-5 h-5 text-[#100562]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-3 text-sm">
              Our team is here to provide personalized assistance for your specific needs.
            </p>
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-4 py-2 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300 text-sm"
            >
              Contact Support
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
