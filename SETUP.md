# Project Setup Guide

A quick guide to understand the structure and get both apps running locally.

## Overview

This workspace contains two apps:
- sipher-web: React + Vite frontend with Tailwind CSS.
- sipher-backend: Node.js/Express API for lead submissions.

## Repository structure

- sipher-web/
  - src/
    - pages/ — route-level screens (Home, About, Services, Contact, etc.)
    - layout/ — app shell components (Header, Footer, LiveChat, MouseCursorEffect)
    - components/ — reusable sections (Hero, ServicesList, TeamSection, etc.)
  - public/ — static assets
  - .env.example — frontend env template
- sipher-backend/
  - server.js — Express server entry
  - src/ — (reserved for future modules)
  - .env.example — backend env template

## Prerequisites

- Node.js 18+ and npm 9+ installed
- Windows PowerShell (commands below are PowerShell-friendly)

## Environment configuration

Create .env files based on the templates before running locally.

- Frontend (sipher-web/.env)
  - VITE_API_BASE=http://localhost:5000

- Backend (sipher-backend/.env)
  - PORT=5000
  - CORS_ORIGIN=http://localhost:5173
  - EMAIL_SERVICE=gmail
  - EMAIL_USER=<EMAIL>
  - EMAIL_PASS=your-app-password
  - LEAD_NOTIFY_TO=<EMAIL>

Note: Use an app-specific password for Gmail or choose another SMTP service.

## Install & run

- Install dependencies:

```powershell
# Frontend
cd sipher-web
npm ci

# Backend (new terminal or after frontend install)
cd ..\sipher-backend
npm ci
```

- Run in development:

```powershell
# Backend (terminal A)
cd sipher-backend
npm run dev

# Frontend (terminal B)
cd sipher-web
npm run dev
```

- Production builds:

```powershell
# Frontend build outputs to sipher-web/dist
cd sipher-web
npm run build

# Backend start
cd ..\sipher-backend
npm start
```

## Frontend notes

- Routing lives in `src/App.jsx`, using components from `src/pages/`.
- Keep pages focused on composition; place reusable blocks in `src/components/`.
- Layout components (Header, Footer, LiveChat, MouseCursorEffect) are in `src/layout/`.
- API base URL is read from `import.meta.env.VITE_API_BASE`.
- Styling uses Tailwind CSS; global config is in `tailwind.config.js`.

## Backend notes

- Express server in `server.js` exposes `POST /api/leads`.
- CORS is controlled by `CORS_ORIGIN`.
- Email notifications use Nodemailer with `EMAIL_*` vars.
- No DB dependency at present; request validation is minimal.

## Conventions

- Component naming: PascalCase for React components/files.
- pages/: 1 file per route (e.g., `Home.jsx`, `About.jsx`).
- sections/components: descriptive nouns (e.g., `TeamSection.jsx`).
- Avoid committing `.env`; share values out-of-band.

## Troubleshooting

- CORS errors: ensure `CORS_ORIGIN` matches the frontend dev URL (default Vite: http://localhost:5173).
- Email errors: verify SMTP credentials; some providers require app passwords or allow-listing.
- Large bundle warning: optional to address later via code-splitting with dynamic `import()`.

## Quick verify (local)

- Backend: `npm run dev` in `sipher-backend` prints a listening port, no errors.
- Frontend: `npm run dev` in `sipher-web` opens the site; `Get Started` form posts to `/api/leads`.

## Ready to share

- .env files are ignored by git.
- `.env.example` exists for both apps.
- Unused/duplicate files have been removed.
- Frontend builds successfully (`npm run build`).
