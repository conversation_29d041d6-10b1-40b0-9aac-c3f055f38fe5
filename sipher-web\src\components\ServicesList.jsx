import React, { useState } from 'react';
import {
  Smartphone,
  Code,
  Globe,
  Package,
  Database,
  Palette,
  Settings,
  Shield,
  Cloud,
  Megaphone,
  Share2,
  Users,
  Search,
  BarChart,
} from 'lucide-react';

const ServicesList = () => {
  const [expandedService, setExpandedService] = useState(null);

  const themeColors = {
    primary: "#100562",
    accent: "#FFE300",
    textLight: "#ffffff",
    textDark: "#333333",
  };

  const services = [
    {
      id: 1,
      title: "Mobile App Development",
      icon: <Smartphone className="w-10 h-10 text-[#FFE300]" />,
      description: "Build innovative mobile apps tailored to your needs.",
      detailedDescription:
        "We offer native and cross-platform app development with seamless performance and intuitive designs.",
      image: "https://img.freepik.com/premium-psd/application-mockup-psd-template_440410-347.jpg?w=740",
      packageLink: "/packages#mobile-app-development",
    },
    {
      id: 2,
      title: "Web Development",
      icon: <Globe className="w-10 h-10 text-[#FFE300]" />,
      description: "Create responsive and dynamic web applications.",
      detailedDescription:
        "From landing pages to complex portals, we deliver scalable and visually stunning web solutions.",
      image: "https://img.freepik.com/free-psd/business-template-design_23-2150316187.jpg?t=st=1738398154~exp=1738401754~hmac=f1a3ceff231df9e56e4154d044494b93a2e49685813f9ebd0d3b78d9b34e3da8&w=826",
      packageLink: "/packages#web-development",
    },
    {
      id: 3,
      title: "Custom Software Development",
      icon: <Code className="w-10 h-10 text-[#FFE300]" />,
      description: "Explore how custom software keeps your business thriving...",
      detailedDescription:
        "Our custom software development services include end-to-end solutions tailored to your specific business needs, from consultation and planning to design, development, testing, deployment, and ongoing support. We deliver scalable, secure, and high-performance solutions that drive business growth and efficiency. We prioritize flexibility and customization to meet evolving market trends.",
      image: "https://img.freepik.com/free-photo/programming-background-with-person-working-with-codes-computer_23-2150010125.jpg?t=st=1737719492~exp=1737723092~hmac=1892a2183136aef1654e2f6636f29c30dabbb95d7c5829be5f164d323c4113ea&w=740",
      packageLink: "/packages#custom-software-development",
    },
    {
      id: 4,
      title: "Software Product Development",
      icon: <Package className="w-10 h-10 text-[#FFE300]" />,
      description: "Deliver the best software products for your business needs...",
      detailedDescription:
        "We assist in designing, developing, and launching software products that align with market demands. From concept to deployment, we focus on innovation, scalability, and user satisfaction. Our agile development process ensures faster time-to-market and continuous improvement to adapt to user feedback.",
      image: "https://img.freepik.com/free-vector/gradient-api-illustration_23-2149370938.jpg?t=st=1738397933~exp=1738401533~hmac=b2a6974609fd6fc1ac170bb5464bb1dc104a96cf2ab4ff7b1e655e72b7f0f4c4&w=740",
      packageLink: "/packages#software-product-development",
    },
    {
      id: 5,
      title: "ERP Software Development",
      icon: <Database className="w-10 h-10 text-[#FFE300]" />,
      description: "Get ERP solutions built for your business purposes...",
      detailedDescription:
        "Our ERP software development services help businesses streamline operations, improve productivity, and achieve better resource planning. We offer customizable and integrated ERP systems designed to automate workflows, enhance data accuracy, and support your strategic decision-making processes.",
      image: "https://img.freepik.com/premium-photo/enterprise-resource-planning-erp-software-system-business-resource-plans-business-people-financial-digital-technology-concept-icons-virtual-screen_55997-2007.jpg?w=740",
      packageLink: "/packages#erp-software-development",
    },
    {
      id: 6,
      title: "UI/UX Design",
      icon: <Palette className="w-10 h-10 text-[#FFE300]" />,
      description: "Design apps and websites with intuitive experiences...",
      detailedDescription:
        "We specialize in creating user-centered designs that enhance usability and improve overall user satisfaction. Our design process includes research, wireframing, prototyping, and testing to ensure we deliver intuitive, engaging, and aesthetically pleasing designs tailored to your business goals.",
      image: "https://img.freepik.com/free-vector/gradient-ui-ux-background_23-2149052117.jpg?t=st=1738397767~exp=1738401367~hmac=442eb6f5bdecf010607063f30461261cd6824c4426d91eb8c5da25559a6fa8a2&w=740",
      packageLink: "/packages#ui-ux-design",
    },
    {
      id: 7,
      title: "Software Maintenance",
      icon: <Settings className="w-10 h-10 text-[#FFE300]" />,
      description: "Ensure optimal performance with reliable maintenance services...",
      detailedDescription:
        "Our software maintenance services include bug fixes, performance optimization, regular updates, and technical support to keep your software functioning smoothly and efficiently. We focus on reducing downtime, enhancing user experience, and keeping your software aligned with the latest technological advancements.",
      image: "https://img.freepik.com/premium-photo/creative-desk-with-laptop-coffee-cup-glowing-binary-code-cogwheel-screen-blurry-window-with-city-view-background-adjusting-app-setting-options-maintenance-repair-fixing-3d-rendering_670147-45272.jpg?w=740",
      packageLink: "/packages#software-maintenance",
    },
    {
      id: 8,
      title: "Cybersecurity Solutions",
      icon: <Shield className="w-10 h-10 text-[#FFE300]" />,
      description: "Protect your business with advanced cybersecurity solutions...",
      detailedDescription:
        "From vulnerability assessments and threat management to secure software development, we help safeguard your digital assets and ensure compliance with security standards. Our services include penetration testing, data encryption, firewalls, and real-time monitoring to keep your business safe from cyber threats.",
      image: "https://img.freepik.com/premium-photo/secure-data-internet-security-data-protection-with-business-technology-privacy-concept-safety-virtual-icon-shield-padlock-cyber-network-connection-businessman-s-hand-while-using-laptop_36367-2432.jpg?w=740",
      packageLink: "/packages#cybersecurity-solutions",
    },
    {
      id: 9,
      title: "Cloud Solutions",
      icon: <Cloud className="w-10 h-10 text-[#FFE300]" />,
      description: "Migrate, manage, and optimize with scalable cloud solutions...",
      detailedDescription:
        "We provide secure and efficient cloud migration, storage, and management services that enable businesses to improve flexibility, reduce costs, and enhance performance. Our expertise includes AWS, Azure, and Google Cloud to deliver seamless cloud operations tailored to your needs.",
      image: "https://img.freepik.com/premium-photo/businessman-holding-virtual-cloud-computing-transfer-data-information-upload-download-application-technology-transformation-concept_50039-1754.jpg?w=740",
      packageLink: "/packages#cloud-solutions",
    },
    {
      id: 10,
      title: "Digital Marketing",
      icon: <Megaphone className="w-10 h-10 text-[#FFE300]" />,
      description: "Grow your online presence with our strategic marketing services...",
      detailedDescription:
        "From SEO and PPC to content marketing and email campaigns, we create tailored strategies to maximize your reach, generate leads, and boost conversions. Our data-driven approach ensures measurable results and optimized marketing ROI for your business.",
      image: "https://etimg.etb2bimg.com/photo/89866384.cms",
      packageLink: "/packages#digital-marketing",
    },
    {
      id: 11,
      title: "Social Media Marketing",
      icon: <Share2 className="w-10 h-10 text-[#FFE300]" />,
      description: "Boost your brand awareness and engagement with expert strategies...",
      detailedDescription:
        "We craft engaging content, manage ad campaigns, and monitor analytics to create meaningful connections with your audience on platforms like Facebook, Instagram, and LinkedIn. Our strategies focus on storytelling and interaction to build loyal communities around your brand.",
      image: "https://img.freepik.com/premium-photo/smartphone-app-announcement-playful-3d-cartoon-style_1021914-1043.jpg?w=900",
      packageLink: "/packages#social-media-marketing",
    },
    {
      id: 12,
      title: "Social Media Management",
      icon: <Users className="w-10 h-10 text-[#FFE300]" />,
      description: "Keep your social media active and effective with our expertise...",
      detailedDescription:
        "We handle content creation, scheduling, audience engagement, and performance tracking to keep your social channels dynamic and impactful. Our focus is on creating a consistent brand voice and growing your online presence effectively.",
      image: "https://img.freepik.com/free-photo/hand-holding-smartphone-social-media-concept_23-2150208253.jpg?t=st=1738397033~exp=1738400633~hmac=262b4c23a41382045cfbf690223d093c8f2969ba11f3bacae6a873295a774c52&w=740",
      packageLink: "/packages#social-media-management",
    },
    {
      id: 13,
      title: "SEO Optimization",
      icon: <Search className="w-10 h-10 text-[#FFE300]" />,
      description: "Boost your search rankings with expert SEO services...",
      detailedDescription:
        "Our SEO optimization services include keyword research, on-page and off-page optimization, content creation, and technical SEO to drive organic traffic and enhance visibility. We help your business stay ahead in search rankings with proven strategies.",
      image: "https://img.freepik.com/free-photo/top-view-cloud-with-word-seo_1134-68.jpg?t=st=1738396947~exp=1738400547~hmac=b6ba1258fe8c00daf68a39aa195a966b811e4ca78634ad0f2ffa9f38cb198055&w=740",
      packageLink: "/packages#seo-optimization",
    },
    {
      id: 14,
      title: "Data Analytics",
      icon: <BarChart className="w-10 h-10 text-[#FFE300]" />,
      description: "Leverage data insights to make informed decisions...",
      detailedDescription:
        "Our data analytics services include data collection, analysis, visualization, and actionable insights to improve operational efficiency and strategic planning. We help you uncover hidden trends and make data-backed decisions for growth.",
      image: "https://img.freepik.com/premium-photo/businessman-working-with-data-management-system-computer-make-report-with-kpi-metrics-connected-database-finance-operations-sales-marketing-kpi-business-performance-indicators_568137-1291.jpg?w=740",
      packageLink: "/packages#data-analytics",
    },
    {
      id: 15,
      title: "E-Commerce Solutions",
      icon: <Package className="w-10 h-10 text-[#FFE300]" />,
      description: "Maximize your online sales with custom e-commerce platforms...",
      detailedDescription:
        "We offer scalable e-commerce development, including shopping carts, payment gateways, product management systems, and mobile-friendly designs. Our solutions are tailored to improve customer experience and increase online sales.",
      image: "https://img.freepik.com/premium-photo/startup-small-business-entrepreneur-confirm-delivery-address-from-customer-order-manag_1253175-89.jpg?w=740",
      packageLink: "/packages#e-commerce-solutions",
    },
    {
      id: 16,
      title: "IT Consulting",
      icon: <Settings className="w-10 h-10 text-[#FFE300]" />,
      description: "Transform your business with expert IT strategy and consulting...",
      detailedDescription:
        "We help identify challenges, propose solutions, and implement strategies to optimize your IT infrastructure and align it with your business objectives. Our experts ensure smooth transitions and future-ready IT ecosystems.",
      image: "https://img.freepik.com/free-photo/people-office-analyzing-checking-finance-graphs_23-2150377131.jpg?t=st=1738396196~exp=1738399796~hmac=d46312807bec377731278c1317e757fe22f6ade8686c0a5d396eef9fd5282a71&w=740",
      packageLink: "/packages#it-consulting",
    },
    {
      id: 17,
      title: "Digital Transformation",
      icon: <Globe className="w-10 h-10 text-[#FFE300]" />,
      description: "Drive innovation and growth with digital transformation services",
      detailedDescription:
        "We help businesses adapt to digital technologies, improve operational efficiency, and enhance customer experiences. Our digital transformation services include cloud migration, automation, data analytics, and more to keep your business competitive and future-ready.",
      image: "https://img.freepik.com/free-photo/representation-user-experience-interface-design_23-2150169850.jpg?t=st=1738396138~exp=1738399738~hmac=d5faf6c4560c3c07dca0f1b6b6c0f92aeb774b7629317f213d1df9fa74169efb&w=740",
      packageLink: "/packages#digital-transformation",
    },
    {
      id: 18,
      title: "Mobile App Marketing",
      icon: <Megaphone className="w-10 h-10 text-[#FFE300]" />,
      description: "Promote your mobile app and reach your target audience",
      detailedDescription:
        "We create tailored marketing strategies to increase app downloads, user engagement, and retention. Our services include app store optimization, social media campaigns, influencer marketing, and more to help your app stand out in the competitive market.",
      image: "https://www.neoito.com/blog/wp-content/uploads/2021/07/App-Marketing-Strategies.png.webp",
    },
  ];

  const toggleService = (id) => {
    setExpandedService(expandedService === id ? null : id);
  };

  return (
    <section id="explore-services" className="py-16 bg-gradient-to-b from-white to-gray-100">
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="relative text-center mb-20 overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-100 to-transparent rounded-full opacity-60 transform translate-x-1/2 -translate-y-1/2"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-yellow-100 to-transparent rounded-full opacity-50 transform -translate-x-1/2 translate-y-1/2"></div>
          </div>

          {/* Subtitle */}
          <div className="mb-4 inline-flex items-center px-6 py-2 rounded-full bg-blue-50 border border-blue-100 shadow-sm">
            <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text uppercase tracking-wide">
              Explore Our Expertise
            </span>
          </div>

          {/* Main Title */}
          <h2 className="text-5xl md:text-6xl font-extrabold mb-6 relative inline-block">
            <span className="bg-gradient-to-r from-[#100562] via-blue-600 to-[#100562] text-transparent bg-clip-text">
              Our Premium Services
            </span>
            <div className="absolute left-0 bottom-0 w-full h-1 bg-[#FFE300] rounded transform -skew-x-12"></div>
          </h2>

          {/* Description */}
          <p className="text-lg text-gray-800 max-w-2xl mx-auto leading-relaxed relative">
            Experience a wide array of innovative solutions tailored to elevate your business and create a lasting impact in the digital world.
          </p>

          {/* Decorative dots */}
          <div className="absolute top-1/2 left-6 w-20 h-20 transform -translate-y-1/2 opacity-30">
            <div className="grid grid-cols-3 gap-2">
              {[...Array(9)].map((_, i) => (
                <div key={i} className="w-2 h-2 bg-blue-600 rounded-full"></div>
              ))}
            </div>
          </div>
          <div className="absolute top-1/2 right-6 w-20 h-20 transform -translate-y-1/2 opacity-30">
            <div className="grid grid-cols-3 gap-2">
              {[...Array(9)].map((_, i) => (
                <div key={i} className="w-2 h-2 bg-blue-600 rounded-full"></div>
              ))}
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <div
              key={service.id}
              className="bg-white shadow-md rounded-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              {/* Service Image */}
              <img
                src={service.image}
                alt={service.title}
                className="w-full h-48 object-cover"
              />
              {/* Service Content */}
              <div className="p-6">
                <div className="flex items-center mb-4">
                  {service.icon}
                  <h3
                    className="ml-3 text-xl font-bold"
                    style={{ color: themeColors.primary }}
                  >
                    {service.title}
                  </h3>
                </div>
                <p className="text-gray-700">{service.description}</p>
                <button
                  onClick={() => toggleService(service.id)}
                  className="mt-4 text-sm font-semibold text-[#FFE300] hover:underline focus:outline-none"
                >
                  {expandedService === service.id
                    ? "Read Less"
                    : "Read More"}
                </button>
                {expandedService === service.id && (
                  <p className="mt-4 text-gray-700">
                    {service.detailedDescription}
                  </p>
                )}
                <a
                  href={service.packageLink}
                  className="mt-4 inline-block text-sm font-semibold text-[#100562] hover:underline focus:outline-none"
                >
                  View Package
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesList;
