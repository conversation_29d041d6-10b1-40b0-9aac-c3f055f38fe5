import React from 'react';

const Hero = () => {
  return (
    <section className="relative flex items-center justify-center h-[65vh] bg-gradient-to-b from-[#FFFE55] to-[#fff] text-white px-6 md:px-12 lg:px-16">
      <div className="text-center space-y-4 md:space-y-6 max-w-3xl">
        <h1 className="text-4xl md:text-5xl text-[#100562] font-bold">
         Portfolio
        </h1>
        <p className="text-lg md:text-xl text-[#100562] font-light">
          Discover our recent works and innovative solutions that empower businesses to succeed.
        </p>

        <a
          href="https://forms.gle/Sqb7pr2SW31C29uJ7"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block mt-6 px-6 py-3 bg-[#100562] text-white rounded-lg hover:bg-[#FFD200] hover:text-[#100562] transition duration-300 font-semibold"
        >
          Contact Us for More
        </a>
      </div>
      {/* Decorative element */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-4 bg-gradient-to-r from-transparent via-white to-transparent"></div>
    </section>
  );
};

export default Hero;
