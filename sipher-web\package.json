{"name": "sipher-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^6.1.3", "@react-three/drei": "^9.120.4", "@react-three/fiber": "^8.17.10", "axios": "^1.7.9", "caniuse-lite": "^1.0.30001712", "emailjs-com": "^3.2.0", "framer-motion": "^11.18.2", "gsap": "^3.12.5", "lucide-react": "^0.471.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-helmet": "^6.1.0", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "react-scroll": "^1.9.0", "react-slick": "^0.30.2", "react-typical": "^0.1.3", "slick-carousel": "^1.8.1", "swiper": "^11.1.15", "three": "^0.171.0"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "vite": "^5.4.8"}}