# Academy Website Redirect Button

A sticky, animated button that redirects users to your academy website. The button appears on all pages with smooth animations and hover effects.

## Features

- ✨ **Smooth Animations**: Uses Framer Motion for professional animations
- 📱 **Responsive Design**: Optimized for both desktop and mobile devices
- 🎨 **Customizable**: Easy configuration through a single config file
- 🔄 **Auto-show**: Appears automatically after a configurable delay
- ❌ **Dismissible**: Users can close the button if they don't want to see it
- 🎯 **Accessible**: Includes proper ARIA labels and keyboard navigation
- 💫 **Interactive**: Hover effects, pulsing animation, and tooltip

## Installation

The Academy button has been automatically integrated into your website and will appear on all pages.

## Configuration

You can customize the button by editing the configuration file:

**File:** `src/config/academyConfig.js`

```javascript
export const academyConfig = {
  // Academy website URL - Update this with your actual academy website URL
  academyUrl: "https://academy.sipherweb.com",
  
  // Button appearance delay (in milliseconds)
  showDelay: 2000,
  
  // Button text configuration
  text: {
    primary: "Visit Our",
    secondary: "Academy Website", 
    tooltip: "Learn new skills at Sipher Academy"
  },
  
  // Position configuration
  position: {
    bottom: "2rem",      // 8 in Tailwind = 2rem
    left: "1rem",        // 4 in Tailwind = 1rem (mobile)
    leftMd: "2rem"       // 8 in Tailwind = 2rem (desktop)
  }
};
```

### Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `academyUrl` | The URL to redirect to when clicked | `"https://academy.sipherweb.com"` |
| `showDelay` | Delay before showing the button (ms) | `2000` |
| `text.primary` | First line of button text | `"Visit Our"` |
| `text.secondary` | Second line of button text | `"Academy Website"` |
| `text.tooltip` | Tooltip text on hover | `"Learn new skills at Sipher Academy"` |

## Positioning

The button is positioned at the bottom-left corner of the screen:
- **Mobile**: 1rem from left edge, 2rem from bottom
- **Desktop**: 2rem from left edge, 2rem from bottom

This positioning ensures it doesn't conflict with the existing LiveChat button (bottom-right).

## Styling

The button uses your existing design system:
- Primary colors: `#100562` (dark blue) and `#FFE300` (yellow)
- Gradient backgrounds matching your brand
- Consistent with your website's visual language

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- Mobile browsers (iOS Safari, Chrome Mobile, etc.)
- Requires JavaScript enabled

## Performance

- Lightweight component (~2KB gzipped)
- Uses efficient animations with Framer Motion
- Lazy-loaded after initial page load
- No impact on Core Web Vitals

## Customization Examples

### Change the URL
```javascript
academyUrl: "https://learn.yourcompany.com"
```

### Modify the text
```javascript
text: {
  primary: "Join Our",
  secondary: "Learning Platform",
  tooltip: "Start learning today!"
}
```

### Adjust timing
```javascript
showDelay: 5000  // Show after 5 seconds
```

## Troubleshooting

### Button not appearing
1. Check that JavaScript is enabled
2. Verify the component is imported in `App.jsx`
3. Check browser console for errors

### Wrong URL opening
1. Update `academyUrl` in the config file
2. Ensure the URL includes `https://`

### Styling issues
1. Check that Tailwind CSS is properly configured
2. Verify Framer Motion is installed
3. Clear browser cache

## Files Modified

- `src/components/AcademyButton.jsx` - Main component
- `src/layout/AcademyButton.jsx` - Layout wrapper
- `src/config/academyConfig.js` - Configuration file
- `src/App.jsx` - Integration point

## Support

For customization help or issues, refer to the component code or contact your development team.
