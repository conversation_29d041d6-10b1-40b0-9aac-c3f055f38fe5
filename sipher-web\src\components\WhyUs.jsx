"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Lightbulb, Rocket, Shield, Users, Cog, TrendingUp, ChevronRight, Code2 } from "lucide-react";

const featuresData = [
  {
    title: "Innovative Solutions",
    description:
      "Pioneering cutting-edge technologies to keep you ahead of the curve. Our solutions are built to evolve with your business needs.",
    icon: Lightbulb,
    color: "bg-gradient-to-br from-purple-600 to-indigo-600",
    stats: { value: "200+", label: "Innovative Projects Delivered" },
  },
  {
    title: "Lightning-Fast Performance",
    description:
      "Optimized for speed and efficiency. Experience blazing-fast load times and seamless scalability for your growing business.",
    icon: Rocket,
    color: "bg-gradient-to-br from-green-500 to-emerald-500",
    stats: { value: "3x", label: "Faster Than Industry Average" },
  },
  {
    title: "Uncompromising Security",
    description:
      "Bank-grade security protocols to safeguard your data. We employ cutting-edge measures to protect against evolving cyber threats.",
    icon: Shield,
    color: "bg-gradient-to-br from-red-500 to-pink-500",
    stats: { value: "99.9%", label: "Uptime with Zero Breaches" },
  },
  {
    title: "Client-Centric Approach",
    description:
      "Your success is our priority. We work closely with you, ensuring our solutions align perfectly with your business objectives.",
    icon: Users,
    color: "bg-gradient-to-br from-blue-500 to-cyan-500",
    stats: { value: "98%", label: "Client Satisfaction Rate" },
  },
  {
    title: "Continuous Optimization",
    description:
      "We never stop improving. Our team constantly refines and optimizes your systems for peak performance and cost-efficiency.",
    icon: Cog,
    color: "bg-gradient-to-br from-yellow-500 to-orange-500",
    stats: { value: "30%", label: "Average Cost Reduction" },
  },
  {
    title: "Data-Driven Growth",
    description:
      "Leverage the power of data analytics to fuel your growth. Our strategies are backed by solid data and proven methodologies.",
    icon: TrendingUp,
    color: "bg-gradient-to-br from-pink-500 to-rose-500",
    stats: { value: "150%", label: "Average Revenue Growth" },
  },
];

const WhyChooseUs = () => {
  const [activeFeature, setActiveFeature] = useState(0);

  return (
    <section className="py-16 text-center bg-gray-50">
      
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
            <Code2 className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-semibold text-blue-600">Why Choose Us</span>
          </div>
          
      <div className="container mx-auto px-4">
        {/* Section Title */}
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-10">
            Our Commitment for
            <span className="relative ml-2 text-[#100562]">
              Your Success
              <span className="absolute left-0 -bottom-1 w-full h-2 bg-[#FFE300] transform -skew-x-12"></span>
            </span>
          </h2>
         {/* paragraph */}
         <div className="mb-10">
         <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We're dedicated to providing you with the best solutions for your business. Our commitment to excellence is reflected in our innovative approach, client-centric philosophy, and unwavering dedication to your success.
          </p>
         </div>
          

          <div className="grid md:grid-cols-2 gap-10 items-center">
            {/* Features List */}
          <div className="space-y-4">
            {featuresData.map((feature, index) => (
              <motion.div
                key={index}
                className={`p-4 rounded-lg cursor-pointer transition-all duration-300 ${
                  activeFeature === index
                    ? feature.color + " text-white shadow-lg"
                    : "bg-white hover:shadow-md"
                }`}
                onClick={() => setActiveFeature(index)}
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center space-x-3">
                  <feature.icon
                    className={`w-6 h-6 ${
                      activeFeature === index ? "text-white" : "text-gray-600"
                    }`}
                  />
                  <h3 className="text-lg font-semibold">{feature.title}</h3>
                  <ChevronRight
                    className={`w-4 h-4 ml-auto ${
                      activeFeature === index ? "text-white" : "text-gray-400"
                    }`}
                  />
                </div>
              </motion.div>
            ))}
          </div>

          {/* Active Feature Details */}
          <motion.div
            key={activeFeature}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white p-6 rounded-lg shadow-lg"
          >
            <div
              className={`w-12 h-12 ${featuresData[activeFeature].color} rounded-full flex items-center justify-center mb-4`}
            >
              {React.createElement(featuresData[activeFeature].icon, {
                className: "w-6 h-6 text-white",
              })}
            </div>
            <h3 className="text-xl font-bold mb-3 text-[#100562]">
              {featuresData[activeFeature].title}
            </h3>
            <p className="text-gray-600 mb-4">
              {featuresData[activeFeature].description}
            </p>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-[#100562]">
                {featuresData[activeFeature].stats.value}
              </span>
              <span className="text-sm text-gray-500">
                {featuresData[activeFeature].stats.label}
              </span>
            </div>
          </motion.div>
        </div>

        {/* Call-to-Action Button */}
        <div className="mt-12 text-center">
          <a
            href="/contact"
            className="inline-flex items-center gap-2 px-8 py-4 bg-[#100562] text-white rounded-xl hover:bg-blue-700 transition-all duration-300"
          >
            Get Started
            <ChevronRight className="ml-2 -mr-1 w-5 h-5" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
