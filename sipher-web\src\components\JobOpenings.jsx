import React from 'react';
import JobOpeningCard from './JobOpeningCard';

const JobOpenings = ({ jobs }) => {
  return (
    <section id='openings' className="current-openings mb-16">
      <h2 className="text-5xl font-extrabold text-[#100562] mb-12 text-center  drop-shadow-lg">
        Current Openings
      </h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {jobs.map((job, index) => (
          <JobOpeningCard key={index} job={job} />
        ))}
      </div>
    </section>
  );
};

export default JobOpenings;
