import React from 'react';
import { Lightbulb, Users2, Target, Rocket, Shield, TrendingUp } from 'lucide-react';

const BenefitsSection = () => {
  const benefits = [
    {
      icon: <Lightbulb className="w-8 h-8 text-[#FFE300]" aria-hidden="true" />,
      title: "Innovation",
      description: "We harness cutting-edge technologies, ensuring that your business stays ahead of the curve with future-proof solutions."
    },
    {
      icon: <Users2 className="w-8 h-8 text-[#FFE300]" aria-hidden="true" />,
      title: "Expert Team",
      description: "Our seasoned professionals bring a wealth of experience and technical knowledge to every project, ensuring top-tier results."
    },
    {
      icon: <Target className="w-8 h-8 text-[#FFE300]" aria-hidden="true" />,
      title: "Customer Focused",
      description: "We believe in building strong, long-lasting relationships by delivering personalized solutions that align with your vision."
    },
    {
      icon: <Rocket className="w-8 h-8 text-[#FFE300]" aria-hidden="true" />,
      title: "Scalable Solutions",
      description: "From small startups to large enterprises, our solutions are designed to grow with your business and adapt to changing needs."
    },
    {
      icon: <Shield className="w-8 h-8 text-[#FFE300]" aria-hidden="true" />,
      title: "Security First",
      description: "We prioritize your data and application security, using the latest protocols to protect your business from cyber threats."
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-[#FFE300]" aria-hidden="true" />,
      title: "Proven Track Record",
      description: "With a portfolio of successful projects, we have the expertise to ensure your business reaches new heights."
    }
  ];

  return (
    <section className="relative py-12 overflow-hidden bg-gradient-to-b from-white via-gray-50 to-white" aria-labelledby="benefits-heading">
      {/* Background Decorations */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
        <div className="absolute -top-16 -left-16 w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-50 rounded-full opacity-30 animate-pulse"></div>
        <div className="absolute top-1/2 -right-16 w-48 h-48 bg-gradient-to-bl from-yellow-100 to-yellow-50 rounded-full opacity-30 animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-16 left-1/4 w-24 h-24 bg-gradient-to-tr from-purple-100 to-purple-50 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-12">
          <span className="inline-block text-xs font-semibold text-[#FFE300] tracking-wider uppercase mb-2">Why Choose Sipher Web</span>
          <h2 id="benefits-heading" className="text-3xl md:text-4xl font-bold text-[#100562] mb-4 relative inline-block">
            Our Commitment to Excellence
            <div className="absolute left-1/2 -translate-x-1/2 -bottom-2 w-16 h-1 bg-[#FFE300] rounded-full">
              <div className="absolute inset-0 bg-[#FFE300] rounded-full animate-pulse"></div>
            </div>
          </h2>
          <p className="text-sm text-gray-700 max-w-2xl mx-auto leading-relaxed">
            At Sipher Web, we combine creativity, innovation, and technical expertise to deliver solutions 
            that drive your business forward. Discover why we are the perfect partner for your digital transformation.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className="group relative bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-500 hover:-translate-y-1 border border-transparent hover:border-blue-100"
              role="article"
              aria-labelledby={`benefit-title-${index}`}
              aria-describedby={`benefit-desc-${index}`}
            >
              {/* Animated gradient border */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-[#100562] to-purple-500 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
              
              {/* Top accent line */}
              <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-500 via-[#100562] to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-t-xl"></div>
              
              <div className="flex flex-col items-center text-center relative">
                {/* Icon container */}
                <div className="mb-4 p-3 rounded-full bg-gradient-to-br from-blue-50 to-white text-[#100562] group-hover:scale-105 transition-all duration-500 shadow-sm group-hover:shadow-md">
                  <div className="transform group-hover:rotate-6 transition-transform duration-500">
                    {benefit.icon}
                  </div>
                </div>
                
                {/* Content */}
                <h3 id={`benefit-title-${index}`} className="text-xl font-bold text-[#100562] mb-2 group-hover:text-blue-700 transition-colors duration-300">
                  {benefit.title}
                </h3>
                <p id={`benefit-desc-${index}`} className="text-sm text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                  {benefit.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
