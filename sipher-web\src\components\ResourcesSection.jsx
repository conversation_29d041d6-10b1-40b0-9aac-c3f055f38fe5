import React from 'react';
import { Book, Download, ArrowRight, FileText, Video, Newspaper } from 'lucide-react';

const ResourcesSection = () => {
  const resources = [
    {
      title: "Complete Web Development Guide",
      type: "E-Book",
      icon: <Book className="w-6 h-6" />,
      downloads: "2.5k",
      image: "https://images.unsplash.com/photo-1516414447565-b14be0adf13e?w=400&h=300&fit=crop&q=80"
    },
    {
      title: "Frontend Architecture Best Practices",
      type: "PDF Guide",
      icon: <FileText className="w-6 h-6" />,
      downloads: "1.8k",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&q=80"
    },
    {
      title: "Advanced React Patterns",
      type: "Video Course",
      icon: <Video className="w-6 h-6" />,
      downloads: "3.2k",
      image: "https://images.unsplash.com/photo-1593720213428-28a5b9e94613?w=400&h=300&fit=crop&q=80"
    }
  ];

  const articles = [
    {
      title: "The Future of Web Development",
      author: "<PERSON>",
      date: "Feb 28, 2024",
      readTime: "5 min read",
      image: "https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=400&h=250&fit=crop&q=80"
    },
    {
      title: "Understanding Modern CSS",
      author: "Emma Davis",
      date: "Feb 25, 2024",
      readTime: "7 min read",
      image: "https://images.unsplash.com/photo-1542831371-29b0f74f9713?w=400&h=250&fit=crop&q=80"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
              <Book className="w-5 h-5 text-[#100562]" />
              <span className="text-sm font-semibold text-[#100562]">Learning Resources</span>
            </div>
            
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Expand Your
              <span className="relative ml-2">
                Knowledge
                <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FFE300] transform -skew-x-12 opacity-80"></div>
              </span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Access our curated collection of learning resources, from comprehensive guides
              to practical tutorials.
            </p>
          </div>

          {/* Featured Resources */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {resources.map((resource, index) => (
              <div
                key={index}
                className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="relative h-48">
                  <img
                    src={resource.image}
                    alt={resource.title}
                    className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-medium text-[#100562]">
                    {resource.type}
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#100562] transition-colors duration-300">
                    {resource.title}
                  </h3>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Download className="w-5 h-5" />
                      <span>{resource.downloads} downloads</span>
                    </div>
                    <a
                      href="#download"
                      className="inline-flex items-center gap-2 text-[#100562] font-medium hover:gap-3 transition-all duration-300"
                    >
                      Download
                      <ArrowRight className="w-5 h-5" />
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Latest Articles */}
          <div className="bg-gray-50 rounded-2xl p-8">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-3">
                <Newspaper className="w-6 h-6 text-[#100562]" />
                <h3 className="text-xl font-bold text-gray-900">Latest Articles</h3>
              </div>
              <a
                href="#all-articles"
                className="text-[#100562] font-medium hover:underline"
              >
                View All
              </a>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {articles.map((article, index) => (
                <div
                  key={index}
                  className="group bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <div className="relative h-48">
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <div className="p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-[#100562] transition-colors duration-300">
                      {article.title}
                    </h4>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>{article.author}</span>
                      <span>•</span>
                      <span>{article.date}</span>
                      <span>•</span>
                      <span>{article.readTime}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default ResourcesSection;