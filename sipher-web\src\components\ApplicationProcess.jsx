import React from 'react';
import { Link } from "react-router-dom";

const ApplicationProcess = () => {
  return (
    <section className="application-process text-center py-16 bg-[#fff] mb-16 rounded-3xl shadow-lg">
      <h2 className="text-5xl font-extrabold text-[#100562] mb-8   ">
        How to Apply
      </h2>
      <p className="text-lg text-gray-800 max-w-2xl mx-auto mb-8">
        Send your resume and cover letter to{' '}
        <a href="mailto:<EMAIL>" className="text-yellow-400 font-semibold hover:underline">
          <EMAIL>
        </a>. We will get back to you as soon as possible.
      </p>
        <Link to="/contact" className="inline-block bg-[#FFE300] hover:bg-[#FFFFFF] text-[#000] font-bold py-3 px-8 rounded-full transition transform duration-300 hover:scale-105 hover:shadow-lg"
        > Contact Us</Link>
       
    </section>
  );
};

export default ApplicationProcess;
