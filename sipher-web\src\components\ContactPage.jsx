import React from 'react';
import { 
  Phone, Mail, MapPin, Linkedin, MessageCircle, Instagram,
  CheckCircle2
} from 'lucide-react';

import ContactForm from './ContactForm';

const Contact = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 via-white to-gray-50">
      {/* Hero Section */}
      <section className="relative pt-32 pb-20 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
          backgroundSize: '48px 48px',
          opacity: '0.05'
        }}></div>

        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
              <MessageCircle className="w-5 h-5 text-[#100562]" />
              <span className="text-sm font-semibold text-[#100562]">Get in Touch</span>
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Let's Start a
              <span className="relative ml-2">
                Conversation
                <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FFE300] transform -skew-x-12 opacity-80"></div>
              </span>
            </h1>
            
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Have questions or want to discuss your project? We're here to help transform 
              your vision into reality.
            </p>
          </div>

          {/* Contact Information Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
            {[
              {
                icon: <Phone className="w-6 h-6" />,
                title: "Call Us",
                info: "+91 91255 45607",
                subInfo: "Mon-Sat: 9:00 AM - 6:00 PM",
                action: "tel:+************"
              },
              {
                icon: <Mail className="w-6 h-6" />,
                title: "Email Us",
                info: "<EMAIL>",
                subInfo: "24/7 Support Available",
                action: "mailto:<EMAIL>"
              },
              {
                icon: <MapPin className="w-6 h-6" />,
                title: "Visit Us",
                info: "Sector G, Jankipuram",
                subInfo: "Lucknow, India",
                action: "#map"
              }
            ].map((item, index) => (
              <a
                key={index}
                href={item.action}
                className="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 text-center"
              >
                <div className="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-6 text-[#100562] group-hover:scale-110 transition-transform duration-300">
                  {item.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-[#100562] font-medium mb-1">{item.info}</p>
                <p className="text-gray-500 text-sm">{item.subInfo}</p>
              </a>
            ))}
          </div>

          {/* Contact Form Section */}
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            
            {/* Contact Form */}
            <ContactForm/>

            {/* Additional Information */}
            <div className="space-y-8">
              {/* Quick Stats */}
                      <div className="grid grid-cols-2 gap-6">
                      {[
                        { value: "15+", label: "Years Experience" },
                        { value: "500+", label: "Projects Delivered" },
                        { value: "24/7", label: "Support" },
                        { value: "100%", label: "Satisfaction" }
                      ].map((stat, index) => (
                        <div key={index} className="bg-white p-6 rounded-xl shadow-md text-center">
                        <div className="text-2xl font-bold text-[#100562] mb-1">{stat.value}</div>
                        <div className="text-sm text-gray-600">{stat.label}</div>
                        </div>
                      ))}
                      </div>

                      {/* Trust Badges */}
                      <div className="bg-white p-8 rounded-xl shadow-md space-y-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">Why Choose Us</h3>
                      {[
                        "ISO 27001 Certified",
                        "Secure Communication",
                        "Quick Response Time",
                        "Expert Team",
                        " 24/7 Support",
                      ].map((item, index) => (
                        <div key={index} className="flex items-center gap-3">
                        <CheckCircle2 className="w-5 h-5 text-green-500" />
                        <span className="text-gray-600">{item}</span>
                        </div>
                      ))}
                      </div>

                     
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-20 bg-gray-50" id="map">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white p-8 rounded-2xl shadow-lg">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Location</h2>
                <p className="text-gray-600">
                  First Floor, C-1/364, Near Muglai Hutz, Sector G, Jankipuram, Tedhi Pulia, Lucknow, India
                </p>
              </div>
              <div className="rounded-xl overflow-hidden h-[400px]">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3559.6177447311635!2d80.91713811503271!3d26.87697388314495!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x399bfd06b4c1d617%3A0x8129a5b18f2e65f!2sTedhi%20Pulia%2C%20Lucknow%2C%20Uttar%20Pradesh!5e0!3m2!1sen!2sin!4v1633106164780!5m2!1sen!2sin"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  title="Sipher Web Location"
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;