import React, { useState, useMemo } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { ArrowRight, ChevronDown, Code, Rocket, Trophy, Monitor, Zap, Shield, BarChart as Chart } from 'lucide-react';
import 'swiper/css';
import { motion, AnimatePresence } from 'framer-motion';
import 'swiper/css/effect-fade';
import 'swiper/css/pagination';
import { Pagination, EffectFade, Autoplay } from 'swiper/modules';
import ContactForm from './GetStartForm';

const slidesData = [
  {
    title: 'Innovative Web Solutions',
    subtitle: 'Future-Proof Your Digital Presence',
    description: 'Cutting-edge web applications built with modern technologies and best practices for scalable, maintainable solutions.',
    features: [
      { icon: Zap, text: 'Lightning Fast', description: 'Optimized Performance' },
      { icon: Code, text: 'Clean Code', description: 'Best Practices' },
      { icon: Monitor, text: 'Responsive', description: 'All Devices' }
    ],
    image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?auto=format&fit=crop&q=80&w=2072',
    color: '#3B82F6',
    cta: 'Start Your Journey'
  },
  {
    title: 'E-Commerce Excellence',
    subtitle: 'Maximize Your Online Revenue',
    description: 'Powerful e-commerce solutions with seamless payment processing, inventory management, and customer analytics.',
    features: [
      { icon: Shield, text: 'Secure', description: 'Protected Payments' },
      { icon: Chart, text: 'Analytics', description: 'Data Insights' },
      { icon: Rocket, text: 'Scalable', description: 'Growth Ready' }
    ],
    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?auto=format&fit=crop&q=80&w=2070',
    color: '#10B981',
    cta: 'Transform Your Store'
  },
  {
    title: 'Digital Growth Strategy',
    subtitle: 'Amplify Your Market Presence',
    description: 'Comprehensive digital marketing solutions with SEO optimization, social media integration, and conversion tracking.',
    features: [
      { icon: Trophy, text: 'SEO Ready', description: 'Rank Higher' },
      { icon: Monitor, text: 'Multi-Channel', description: 'Wider Reach' },
      { icon: Chart, text: 'ROI Focused', description: 'Better Returns' }
    ],
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80&w=2015',
    color: '#8B5CF6',
    cta: 'Scale Your Business'
  }
];

const HeroSwiper = () => {
  const [activeSlide, setActiveSlide] = useState(0);
  const [isFormOpen, setIsFormOpen] = useState(false);

  const handleSlideChange = (swiper) => {
    setActiveSlide(swiper.realIndex);
  };

  const scrollToContent = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: 'smooth'
    });
  };

  const openForm = () => {
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
  };

  // Memoize slides to prevent unnecessary re-renders
  const slides = useMemo(() => slidesData.map((slide, index) => (
    <SwiperSlide key={index}>
      <div
        className="w-full h-full bg-cover bg-center flex items-center justify-center transition-opacity duration-1000 ease-in-out"
        style={{
          backgroundImage: `url(${slide.image})`,
          backgroundPosition: 'center',
          backgroundSize: 'cover',
          opacity: activeSlide === index ? 1 : 0,
        }}
      >
        <div className="container mx-auto px-4 md:px-8 py-8 sm:py-12">
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="space-y-4 sm:space-y-6">
              <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-xs font-medium tracking-wide uppercase border border-white/30 hover:bg-white/30 transition-all">
                {slide.subtitle}
              </span>

              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight">
                {slide.title}
              </h1>

              <p className="text-xs sm:text-sm md:text-base opacity-90 max-w-3xl mx-auto leading-relaxed">
                {slide.description}
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {slide.features.map((feature, idx) => (
                  <div
                    key={idx}
                    className="group flex flex-col items-center space-y-2 p-4 rounded-lg bg-white/10 backdrop-blur-sm border border-white/30 hover:bg-white/20 transition-all cursor-pointer"
                  >
                    <feature.icon className="w-6 h-6 group-hover:scale-105 transition-transform duration-300 text-white/90" />
                    <span className="text-xs font-medium">{feature.text}</span>
                    <span className="text-xs text-white/70">{feature.description}</span>
                  </div>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
                <button
                  className="group px-4 py-2 sm:px-6 sm:py-3 bg-white text-gray-900 font-medium rounded-full hover:bg-opacity-90 transition-all transform hover:scale-105 shadow-lg flex items-center gap-2"
                  onClick={openForm}
                >
                  {slide.cta}
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </button>
                <button
                  className="px-4 py-2 sm:px-6 sm:py-3 border-2 border-white text-white font-medium rounded-full hover:bg-white hover:text-gray-900 transition-all duration-300"
                  onClick={() => window.location.href = '/portfolio'}
                >
                  View Portfolio
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SwiperSlide>
  )), [activeSlide]);

  return (
    <div className="relative w-full h-screen overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-transparent z-10" />

      <Swiper
        modules={[Pagination, EffectFade, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        pagination={{
          clickable: true,
          bulletClass: 'swiper-pagination-bullet !bg-white/50 !w-2 !h-2 !transition-all',
          bulletActiveClass: 'swiper-pagination-bullet-active !bg-white !w-6'
        }}
        effect="fade"
        autoplay={{ delay: 6000, disableOnInteraction: false }}
        loop={true}
        onSlideChange={handleSlideChange}
        className="relative z-20 h-full"
      >
        {slides}
      </Swiper>

      <button
        onClick={scrollToContent}
        className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-30 text-white animate-bounce hover:text-white/80 transition-colors duration-300"
        aria-label="Scroll to content"
      >
        <ChevronDown className="w-8 h-8" />
      </button>

      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white p-6 sm:p-8 rounded-lg shadow-lg relative"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <button
                className="absolute top-2 right-2 text-gray-700 hover:text-black"
                onClick={closeForm}
              >
                ✖
              </button>
              <ContactForm />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default React.memo(HeroSwiper);
