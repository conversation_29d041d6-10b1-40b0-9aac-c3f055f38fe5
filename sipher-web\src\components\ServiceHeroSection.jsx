import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Settings, Spa<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>C<PERSON>, Shield } from "lucide-react";
import { motion, AnimatePresence } from 'framer-motion';
import ContactForm from "./GetStartForm";

const ServiceHeroSection = () => {
  const [showForm, setShowForm] = useState(false);

  const handleGetStartedClick = (e) => {
    e.preventDefault();
    setShowForm(true);
  };

  const closeForm = () => {
    setShowForm(false);
  };

  return (
    <section className="relative min-h-[100vh] flex items-center justify-center overflow-hidden bg-gradient-to-b from-white via-gray-50 to-white">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-96 h-96 bg-blue-200/40 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-200/40 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-96 h-96 bg-purple-200/40 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
      </div>

      {/* Floating Icons */}
      <div className="absolute inset-0 pointer-events-none">
        <Settings className="absolute top-24 left-[15%] w-12 h-12 text-blue-400/50 animate-float" />
        <Sparkles className="absolute top-36 right-[20%] w-8 h-8 text-yellow-400/50 animate-float animation-delay-2000" />
        <Zap className="absolute bottom-28 left-[25%] w-10 h-10 text-purple-400/50 animate-float animation-delay-4000" />
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          {/* Subtitle */}
          <div className="mb-6 inline-flex items-center px-6 py-2 rounded-full bg-blue-50 border border-blue-100 shadow-md">
            <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">
              Transforming Ideas into Solutions
            </span>
          </div>

          {/* Main Title */}
          <h1 className="text-5xl sm:text-6xl md:text-7xl font-extrabold mb-8">
            <span className="bg-gradient-to-r from-[#100562] via-blue-600 to-[#100562] text-transparent bg-clip-text">
              Exceptional Services
            </span>
            <br />
            <span className="relative inline-block mt-2">
              Tailored for You
              <div className="absolute left-0 bottom-0 w-full h-1 bg-[#FFE300] transform -skew-x-12"></div>
            </span>
          </h1>

          {/* Description */}
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Discover a wide range of innovative services crafted to meet your business needs. Experience the perfect blend of technology and creativity.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <a
              href="#explore-services"
              className="group relative px-8 py-4 bg-[#100562] text-white rounded-full overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="relative z-10">Explore Services</span>
              <div className="absolute inset-0 bg-[#FEE300] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left rounded-full border-2 border-[#100562]"></div>
            </a>

            <a
              href="#get-started"
              onClick={handleGetStartedClick}
              className="group px-8 py-4 bg-white text-[#100562] rounded-full border-2 border-[#100562] hover:bg-[#FFE300] transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl"
            >
              Get Started
            </a>
          </div>
        </div>
      </div>

      {/* Form Popup */}
      <AnimatePresence>
        {showForm && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white p-6 rounded-xl shadow-lg max-w-lg w-full relative"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl font-bold"
                onClick={closeForm}
                aria-label="Close"
              >
                ✖
              </button>
              <ContactForm />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <ChevronDown className="w-6 h-6 text-gray-400" />
      </div>

      {/* Trust Badges */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex flex-wrap justify-center items-center gap-8">
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Shield className="w-5 h-5" />
          Secure & Reliable
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Sparkles className="w-5 h-5" />
          Top-Rated Experts
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <BarChart className="w-5 h-5" />
          Proven Success
        </div>
      </div>
    </section>
  );
};

export default ServiceHeroSection;