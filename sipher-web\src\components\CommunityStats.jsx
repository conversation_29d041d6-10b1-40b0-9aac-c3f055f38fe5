import React from 'react';
import { Users2, MessageCircle, Globe2, Award } from 'lucide-react';

const CommunityStats = () => {
  const stats = [
    {
      icon: <Users2 className="w-8 h-8" />,
      value: "50,000+",
      label: "Active Members",
      description: "Professionals worldwide"
    },
    {
      icon: <MessageCircle className="w-8 h-8" />,
      value: "25,000+",
      label: "Forum Posts",
      description: "Knowledge shared"
    },
    {
      icon: <Globe2 className="w-8 h-8" />,
      value: "150+",
      label: "Countries",
      description: "Global presence"
    },
    {
      icon: <Award className="w-8 h-8" />,
      value: "1,000+",
      label: "Expert Contributors",
      description: "Industry leaders"
    }
  ];

  return (
    <section className="py-16 bg-[#100562] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
          backgroundSize: '24px 24px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl text-[#FFE300] mb-4">
                {stat.icon}
              </div>
              <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
              <div className="text-lg font-medium text-blue-200 mb-1">{stat.label}</div>
              <div className="text-sm text-blue-200/80">{stat.description}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default CommunityStats;